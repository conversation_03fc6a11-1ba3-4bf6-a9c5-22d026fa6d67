spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_DISCOVERY_HOST:nacos-discovery-staging.item.com:8848}
        namespace: ${NACOS_DISCOVERY_NS:item}
        username: ${NACOS_DISCOVERY_USER:item}
        password: ${NACOS_DISCOVERY_PASSWD:YjZzJWXpSPTiXkGB}
        register-enabled: ${NACOS_DISCOVERY_REGISTER:false}
        enabled: true
      config:
        file-extension: yml
        server-addr: ${NACOS_CONFIG_CONFIG_HOST:nacos-config-staging.item.com:8848}
        namespace: ${NACOS_CONFIG_NS:item}
        username: ${NACOS_CONFIG_USER:item}
        password: ${NACOS_CONFIG_PASSWD:YjZzJWXpSPTiXkGB}
        timeout: 5000
        max-retry: 3
        config-long-poll-timeout: 60000
        config-retry-time: 3000
        enableRemoteSyncConfig: true
        enabled: true