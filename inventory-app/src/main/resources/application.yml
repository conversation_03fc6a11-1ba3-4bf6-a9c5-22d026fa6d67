server:
  port: 8088

spring:
  application:
    name: inventory-app
  profiles:
    active: dev
  config:
    import:
      - classpath:bootstrap.yml
      - nacos:${spring.application.name}-${spring.profiles.active}.yml
      - optional:classpath:development.yml


  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
xms:
  tenant:
    tenant-column: isolationId
    enabled: true
