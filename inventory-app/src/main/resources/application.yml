server:
  port: 8088

spring:
  application:
    name: inventory-app
  profiles:
    active: dev
  config:
    import:
      - classpath:bootstrap.yml
      - nacos:${spring.application.name}-${spring.profiles.active}.yml
      - optional:classpath:development.yml
      - optional:classpath:development-${spring.profiles.active}.yaml
    # 设置本地配置优先级高于远程配置
    override-none: false
    override-system-properties: false


  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
xms:
  tenant:
    tenant-column: isolationId
    enabled: true
