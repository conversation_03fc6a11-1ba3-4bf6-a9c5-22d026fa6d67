package com.item.inventory.interfaces.rest;

import com.item.inventory.application.location.dto.CreateLocationCmd;
import com.item.inventory.application.location.dto.DockOperator;
import com.item.inventory.application.location.dto.LocationDto;
import com.item.inventory.application.location.dto.LocationSearch;
import com.item.inventory.application.location.dto.UpdateLocationCmd;
import com.item.inventory.application.location.service.LocationApplicationService;
import com.item.inventory.domain.location.model.entity.Location;
import com.item.xms.annotation.UpdateWithNulls;
import com.item.xms.persistence.query.PageResult;
import com.item.xms.web.model.R;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Location Management
 * Manages operations related to warehouse and facility locations, including creation, search, and dock management.
 */
@RequiredArgsConstructor
@RestController
public class LocationController {

    private final LocationApplicationService locationApplicationService;

    /**
     * Create Location
     * Creates a new location record based on the provided location details.
     * @param cmd Object containing the location data to be created
     * @return R containing the ID of the created location
     */
    @PostMapping("/location")
    public R<String> createLocation(@Valid @RequestBody CreateLocationCmd cmd) {
        LocationDto location = locationApplicationService.createLocation(cmd);
        return R.ok(location.id());
    }

    /**
     * Update Location
     * Updates an existing location with new information.
     * @param cmd Object containing the location update data and identifier
     * @return R indicating success or failure
     */
    @PutMapping("/location")
    public R<Void> updateLocation(@Valid @RequestBody @UpdateWithNulls(Location.class) UpdateLocationCmd cmd) {
        locationApplicationService.updateLocation(cmd);
        return R.ok();
    }

    /**
     * Import Location
     * Processes a batch import of location records, typically from an external source.
     * @param batch List of location data objects to be imported
     * @return R containing a list of created location DTOs
     */
    @PostMapping("/location/import")
    public R<List<LocationDto>> importLocation(@Valid @RequestBody List<CreateLocationCmd> batch) {
        return R.ok(locationApplicationService.importLocation(batch));
    }

    /**
     * Get Location
     * Retrieves a specific location record by its unique identifier.
     * @param id The unique identifier of the location
     * @return R containing the requested location data
     */
    @GetMapping("/location/{id}")
    public R<LocationDto> getLocation(@PathVariable("id") String id) {
        LocationDto locationDto = locationApplicationService.getLocation(id);
        return R.ok(locationDto);
    }

    /**
     * Search Location
     * Retrieves a list of locations matching the search criteria, sorted by name.
     * @param search Object containing filtering options
     * @return R containing a list of location records matching the criteria
     */
    @PostMapping("/location/search")
    public R<List<LocationDto>> searchLocation(@RequestBody LocationSearch search) {
        List<LocationDto> locationDtoList = locationApplicationService.search(search);
        return R.ok(locationDtoList);
    }

    /**
     * Search Location By Paging
     * Retrieves a paginated list of locations based on search criteria, sorted by name.
     * @param search Object containing filtering and pagination options
     * @return R containing a PageResult with location records matching the criteria
     */
    @PostMapping("/location/search-by-paging")
    public R<PageResult<LocationDto>> searchLocationByPaging(@RequestBody LocationSearch search) {
        return R.ok(locationApplicationService.searchByPaging(search));
    }

    /**
     * Delete Location
     * Removes a location record from the system.
     * @param id The unique identifier of the location to delete
     * @return R containing the ID of the deleted location
     */
    @DeleteMapping("/location/{id}")
    public R<String> deleteLocation(@PathVariable("id") String id) {
        locationApplicationService.deleteLocation(id);
        return R.ok(id);
    }

    /**
     * Reserve Dock
     * Marks a dock location as reserved for a specific purpose.
     * @param id The unique identifier of the dock location
     * @param operator Object containing reservation details including who is reserving and why
     * @return R indicating success or failure
     */
    @PutMapping("/location/{id}/reserve")
    public R<Void> reserveDock(@PathVariable("id") String id, @RequestBody DockOperator.ReserveDock operator) {
        locationApplicationService.reserveDock(id, operator);
        return R.ok();
    }

    /**
     * Release Dock
     * Removes a reservation from a dock location, making it available.
     * @param id The unique identifier of the dock location
     * @return R indicating success or failure
     */
    @PutMapping("/location/{id}/release")
    public R<Void> releaseDock(@PathVariable("id") String id) {
        locationApplicationService.releaseDock(id);
        return R.ok();
    }

    /**
     * Occupy Dock
     * Marks a dock location as currently in use or occupied.
     * @param id The unique identifier of the dock location
     * @param operator Object containing occupation details
     * @return R indicating success or failure
     */
    @PutMapping("/location/{id}/occupy")
    public R<Void> occupyDock(@PathVariable("id") String id, @RequestBody DockOperator.OccupyDock operator) {
        locationApplicationService.occupyDock(id, operator);
        return R.ok();
    }

}
