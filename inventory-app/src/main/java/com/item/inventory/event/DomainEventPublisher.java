package com.item.inventory.event;

import com.item.common.tenant.WmsTenantFieldProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component("inventoryDomainEventPublisher")
public class DomainEventPublisher {

    @Autowired(required = false)
    @Qualifier("inventoryRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Autowired(required = false)
    private WmsTenantFieldProvider tenantFieldProvider;

    public void sendMessage(String topic, Object eventData) {
        if (Objects.isNull(rabbitTemplate)) {
            log.debug("Not configured rabbit template, skip sending message");
            return;
        }
        if (StringUtils.isEmpty(topic)) {
            throw new IllegalArgumentException("Topic can not be empty");
        }
        if (eventData == null) {
            throw new IllegalArgumentException("Domain event data can not be null");
        }
        Event event = Event.builder()
                .companyId(getTenantValue(WmsTenantFieldProvider.TENANT_ID_FIELD))
                .facilityId(getTenantValue(WmsTenantFieldProvider.FACILITY_ID_FIELD))
                .payload(eventData)
                .build();
        rabbitTemplate.convertAndSend(topic, event);
    }

    private String getTenantValue(String key) {
        if (Objects.nonNull(tenantFieldProvider)) {
            return tenantFieldProvider.getTenantIdField(key);
        }

        return Strings.EMPTY;
    }

}
