package com.item.inventory.domain.inventory.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.item.inventory.domain.common.BaseEntity;
import com.item.inventory.domain.inventory.model.valueobject.DiscrepancyFlag;
import com.item.inventory.domain.inventory.model.valueobject.InventoryChannel;
import com.item.inventory.domain.inventory.model.valueobject.InventoryMode;
import com.item.inventory.domain.inventory.model.valueobject.InventoryStatus;
import com.item.inventory.infrastructure.annotation.InventoryIsolation;
import com.item.inventory.infrastructure.annotation.JsonTableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@InventoryIsolation
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName(value = "`inventory_service`.doc_inventory", autoResultMap = true)
public class Inventory extends BaseEntity {

    @TableId(type = IdType.AUTO)
    public Long id;

    public String customerId;
    public String titleId;

    public String itemId;
    public Double qty;
    public String uomId;
    public String sn;
    public String uom;
    public Double baseQty;
    public Double secondaryQty;
    public String secondaryUomId;

    public InventoryStatus status;
    public InventoryStatus preStatus;
    public String type;
    public InventoryMode mode;
    public InventoryChannel channel;

    public String lotNo;
    public LocalDateTime expirationDate;
    public LocalDateTime mfgDate;
    public Integer shelfLifeDays;

    public String lpId;
    public String locationId;
    public String receiptId;
    public String orderId;
    public String adjustmentId;

    public String originalLPId;
    public Double originalBaseQty;
    public LocalDateTime originalCreatedTime;

    public String supplierId;
    public String receiveTaskId;
    public String putAwayTaskId;
    public String pickTaskId;
    public String packTaskId;
    public String loadTaskId;
    public String workflowTaskId;
    public String replenishTaskId;
    public String movementTaskId;

    public String dynTxtPropertyValue01;
    public String dynTxtPropertyValue02;
    public String dynTxtPropertyValue03;
    public String dynTxtPropertyValue04;
    public String dynTxtPropertyValue05;
    public String dynTxtPropertyValue06;
    public String dynTxtPropertyValue07;
    public String dynTxtPropertyValue08;
    public String dynTxtPropertyValue09;
    public String dynTxtPropertyValue10;
    public String dynTxtPropertyValue11;
    public String dynTxtPropertyValue12;
    public String dynTxtPropertyValue13;
    public String dynTxtPropertyValue14;
    public String dynTxtPropertyValue15;
    public String dynTxtPropertyValue16;
    public String dynTxtPropertyValue17;
    public String dynTxtPropertyValue18;
    public String dynTxtPropertyValue19;
    public String dynTxtPropertyValue20;

    public LocalDateTime dynDatePropertyValue01;
    public LocalDateTime dynDatePropertyValue02;
    public LocalDateTime dynDatePropertyValue03;
    public LocalDateTime dynDatePropertyValue04;
    public LocalDateTime dynDatePropertyValue05;
    public LocalDateTime dynDatePropertyValue06;
    public LocalDateTime dynDatePropertyValue07;
    public LocalDateTime dynDatePropertyValue08;
    public LocalDateTime dynDatePropertyValue09;
    public LocalDateTime dynDatePropertyValue10;

    @JsonTableField
    public List<InventoryProfile> profiles;

    public LocalDateTime receivedTime;
    public String receivedBy;
    
    public String palletNo;

    public LocalDateTime adjustOutTime;
    public String adjustOutBy;

    public LocalDateTime shippedTime;
    public String shippedBy;

    public DiscrepancyFlag discrepancyFlag;

    public LocalDateTime discrepancyReportTime;

    public String loadId;
    public String trackingNo;

    public Long originalId;

    public static class Builder<T extends Builder<T>> {

        protected Inventory inventory = new Inventory();

        protected Builder() {
        }

        @SuppressWarnings("unchecked")
        protected T self() {
            return (T) this;
        }

        public T id(Long id) {
            inventory.id = id;
            return self();
        }

        public T customerId(String customerId) {
            inventory.customerId = customerId;
            return self();
        }

        public T titleId(String titleId) {
            inventory.titleId = titleId;
            return self();
        }

        public T itemId(String itemId) {
            inventory.itemId = itemId;
            return self();
        }

        public T qty(Double qty) {
            inventory.qty = qty;
            return self();
        }

        public T uomId(String uomId) {
            inventory.uomId = uomId;
            return self();
        }

        public T sn(String sn) {
            inventory.sn = sn;
            return self();
        }

        public T uom(String uom) {
            inventory.uom = uom;
            return self();
        }

        public T baseQty(Double baseQty) {
            inventory.baseQty = baseQty;
            return self();
        }

        public T secondaryQty(Double secondaryQty) {
            inventory.secondaryQty = secondaryQty;
            return self();
        }

        public T secondaryUomId(String secondaryUomId) {
            inventory.secondaryUomId = secondaryUomId;
            return self();
        }

        public T status(InventoryStatus status) {
            inventory.status = status;
            return self();
        }

        public T preStatus(InventoryStatus preStatus) {
            inventory.preStatus = preStatus;
            return self();
        }

        public T type(String type) {
            inventory.type = type;
            return self();
        }

        public T mode(InventoryMode mode) {
            inventory.mode = mode;
            return self();
        }

        public T channel(InventoryChannel channel) {
            inventory.channel = channel;
            return self();
        }

        public T lotNo(String lotNo) {
            inventory.lotNo = lotNo;
            return self();
        }

        public T expirationDate(LocalDateTime expirationDate) {
            inventory.expirationDate = expirationDate;
            return self();
        }

        public T mfgDate(LocalDateTime mfgDate) {
            inventory.mfgDate = mfgDate;
            return self();
        }

        public T shelfLifeDays(Integer shelfLifeDays) {
            inventory.shelfLifeDays = shelfLifeDays;
            return self();
        }

        public T lpId(String lpId) {
            inventory.lpId = lpId;
            return self();
        }

        public T locationId(String locationId) {
            inventory.locationId = locationId;
            return self();
        }

        public T receiptId(String receiptId) {
            inventory.receiptId = receiptId;
            return self();
        }

        public T orderId(String orderId) {
            inventory.orderId = orderId;
            return self();
        }

        public T adjustmentId(String adjustmentId) {
            inventory.adjustmentId = adjustmentId;
            return self();
        }

        public T originalLPId(String originalLPId) {
            inventory.originalLPId = originalLPId;
            return self();
        }

        public T originalBaseQty(Double originalBaseQty) {
            inventory.originalBaseQty = originalBaseQty;
            return self();
        }

        public T originalCreatedTime(LocalDateTime originalCreatedTime) {
            inventory.originalCreatedTime = originalCreatedTime;
            return self();
        }

        public T supplierId(String supplierId) {
            inventory.supplierId = supplierId;
            return self();
        }

        public T receiveTaskId(String receiveTaskId) {
            inventory.receiveTaskId = receiveTaskId;
            return self();
        }

        public T putAwayTaskId(String putAwayTaskId) {
            inventory.putAwayTaskId = putAwayTaskId;
            return self();
        }

        public T pickTaskId(String pickTaskId) {
            inventory.pickTaskId = pickTaskId;
            return self();
        }

        public T packTaskId(String packTaskId) {
            inventory.packTaskId = packTaskId;
            return self();
        }

        public T loadTaskId(String loadTaskId) {
            inventory.loadTaskId = loadTaskId;
            return self();
        }

        public T dynTxtPropertyValue01(String dynTxtPropertyValue01) {
            inventory.dynTxtPropertyValue01 = dynTxtPropertyValue01;
            return self();
        }

        public T dynTxtPropertyValue02(String dynTxtPropertyValue02) {
            inventory.dynTxtPropertyValue02 = dynTxtPropertyValue02;
            return self();
        }

        public T dynTxtPropertyValue03(String dynTxtPropertyValue03) {
            inventory.dynTxtPropertyValue03 = dynTxtPropertyValue03;
            return self();
        }

        public T dynTxtPropertyValue04(String dynTxtPropertyValue04) {
            inventory.dynTxtPropertyValue04 = dynTxtPropertyValue04;
            return self();
        }

        public T dynTxtPropertyValue05(String dynTxtPropertyValue05) {
            inventory.dynTxtPropertyValue05 = dynTxtPropertyValue05;
            return self();
        }

        public T dynTxtPropertyValue06(String dynTxtPropertyValue06) {
            inventory.dynTxtPropertyValue06 = dynTxtPropertyValue06;
            return self();
        }

        public T dynTxtPropertyValue07(String dynTxtPropertyValue07) {
            inventory.dynTxtPropertyValue07 = dynTxtPropertyValue07;
            return self();
        }

        public T dynTxtPropertyValue08(String dynTxtPropertyValue08) {
            inventory.dynTxtPropertyValue08 = dynTxtPropertyValue08;
            return self();
        }

        public T dynTxtPropertyValue09(String dynTxtPropertyValue09) {
            inventory.dynTxtPropertyValue09 = dynTxtPropertyValue09;
            return self();
        }

        public T dynTxtPropertyValue10(String dynTxtPropertyValue10) {
            inventory.dynTxtPropertyValue10 = dynTxtPropertyValue10;
            return self();
        }

        public T dynTxtPropertyValue11(String dynTxtPropertyValue11) {
            inventory.dynTxtPropertyValue11 = dynTxtPropertyValue11;
            return self();
        }

        public T dynTxtPropertyValue12(String dynTxtPropertyValue12) {
            inventory.dynTxtPropertyValue12 = dynTxtPropertyValue12;
            return self();
        }

        public T dynTxtPropertyValue13(String dynTxtPropertyValue13) {
            inventory.dynTxtPropertyValue13 = dynTxtPropertyValue13;
            return self();
        }

        public T dynTxtPropertyValue14(String dynTxtPropertyValue14) {
            inventory.dynTxtPropertyValue14 = dynTxtPropertyValue14;
            return self();
        }

        public T dynTxtPropertyValue15(String dynTxtPropertyValue15) {
            inventory.dynTxtPropertyValue15 = dynTxtPropertyValue15;
            return self();
        }

        public T dynTxtPropertyValue16(String dynTxtPropertyValue16) {
            inventory.dynTxtPropertyValue16 = dynTxtPropertyValue16;
            return self();
        }

        public T dynTxtPropertyValue17(String dynTxtPropertyValue17) {
            inventory.dynTxtPropertyValue17 = dynTxtPropertyValue17;
            return self();
        }

        public T dynTxtPropertyValue18(String dynTxtPropertyValue18) {
            inventory.dynTxtPropertyValue18 = dynTxtPropertyValue18;
            return self();
        }

        public T dynTxtPropertyValue19(String dynTxtPropertyValue19) {
            inventory.dynTxtPropertyValue19 = dynTxtPropertyValue19;
            return self();
        }

        public T dynTxtPropertyValue20(String dynTxtPropertyValue20) {
            inventory.dynTxtPropertyValue20 = dynTxtPropertyValue20;
            return self();
        }

        public T dynDatePropertyValue01(LocalDateTime dynDatePropertyValue01) {
            inventory.dynDatePropertyValue01 = dynDatePropertyValue01;
            return self();
        }

        public T dynDatePropertyValue02(LocalDateTime dynDatePropertyValue02) {
            inventory.dynDatePropertyValue02 = dynDatePropertyValue02;
            return self();
        }

        public T dynDatePropertyValue03(LocalDateTime dynDatePropertyValue03) {
            inventory.dynDatePropertyValue03 = dynDatePropertyValue03;
            return self();
        }

        public T dynDatePropertyValue04(LocalDateTime dynDatePropertyValue04) {
            inventory.dynDatePropertyValue04 = dynDatePropertyValue04;
            return self();
        }

        public T dynDatePropertyValue05(LocalDateTime dynDatePropertyValue05) {
            inventory.dynDatePropertyValue05 = dynDatePropertyValue05;
            return self();
        }

        public T dynDatePropertyValue06(LocalDateTime dynDatePropertyValue06) {
            inventory.dynDatePropertyValue06 = dynDatePropertyValue06;
            return self();
        }

        public T dynDatePropertyValue07(LocalDateTime dynDatePropertyValue07) {
            inventory.dynDatePropertyValue07 = dynDatePropertyValue07;
            return self();
        }

        public T dynDatePropertyValue08(LocalDateTime dynDatePropertyValue08) {
            inventory.dynDatePropertyValue08 = dynDatePropertyValue08;
            return self();
        }

        public T dynDatePropertyValue09(LocalDateTime dynDatePropertyValue09) {
            inventory.dynDatePropertyValue09 = dynDatePropertyValue09;
            return self();
        }

        public T dynDatePropertyValue10(LocalDateTime dynDatePropertyValue10) {
            inventory.dynDatePropertyValue10 = dynDatePropertyValue10;
            return self();
        }

        public T profiles(List<InventoryProfile> profiles) {
            inventory.profiles = profiles;
            return self();
        }

        public T receivedTime(LocalDateTime receivedTime) {
            inventory.receivedTime = receivedTime;
            return self();
        }

        public T receivedBy(String receivedBy) {
            inventory.receivedBy = receivedBy;
            return self();
        }

        public T palletNo(String palletNo) {
            inventory.palletNo = palletNo;
            return self();
        }

        public T adjustOutTime(LocalDateTime adjustOutTime) {
            inventory.adjustOutTime = adjustOutTime;
            return self();
        }

        public T adjustOutBy(String adjustOutBy) {
            inventory.adjustOutBy = adjustOutBy;
            return self();
        }

        public T shippedTime(LocalDateTime shippedTime) {
            inventory.shippedTime = shippedTime;
            return self();
        }

        public T shippedBy(String shippedBy) {
            inventory.shippedBy = shippedBy;
            return self();
        }

        public Inventory build() {
            return inventory;
        }

        protected <C extends Inventory> C build(C inv) {
            inv.id = inventory.id;
            inv.customerId = inventory.customerId;
            inv.titleId = inventory.titleId;
            inv.itemId = inventory.itemId;
            inv.qty = inventory.qty;
            inv.uomId = inventory.uomId;
            inv.sn = inventory.sn;
            inv.uom = inventory.uom;
            inv.baseQty = inventory.baseQty;
            inv.secondaryQty = inventory.secondaryQty;
            inv.secondaryUomId = inventory.secondaryUomId;
            inv.status = inventory.status;
            inv.preStatus = inventory.preStatus;
            inv.type = inventory.type;
            inv.mode = inventory.mode;
            inv.channel = inventory.channel;
            inv.lotNo = inventory.lotNo;
            inv.expirationDate = inventory.expirationDate;
            inv.mfgDate = inventory.mfgDate;
            inv.shelfLifeDays = inventory.shelfLifeDays;
            inv.lpId = inventory.lpId;
            inv.locationId = inventory.locationId;
            inv.receiptId = inventory.receiptId;
            inv.orderId = inventory.orderId;
            inv.adjustmentId = inventory.adjustmentId;
            inv.originalLPId = inventory.originalLPId;
            inv.originalBaseQty = inventory.originalBaseQty;
            inv.originalCreatedTime = inventory.originalCreatedTime;
            inv.dynTxtPropertyValue01 = inventory.dynTxtPropertyValue01;
            inv.dynTxtPropertyValue02 = inventory.dynTxtPropertyValue02;
            inv.dynTxtPropertyValue03 = inventory.dynTxtPropertyValue03;
            inv.dynTxtPropertyValue04 = inventory.dynTxtPropertyValue04;
            inv.dynTxtPropertyValue05 = inventory.dynTxtPropertyValue05;
            inv.dynTxtPropertyValue06 = inventory.dynTxtPropertyValue06;
            inv.dynTxtPropertyValue07 = inventory.dynTxtPropertyValue07;
            inv.dynTxtPropertyValue08 = inventory.dynTxtPropertyValue08;
            inv.dynTxtPropertyValue09 = inventory.dynTxtPropertyValue09;
            inv.dynTxtPropertyValue10 = inventory.dynTxtPropertyValue10;
            inv.dynTxtPropertyValue11 = inventory.dynTxtPropertyValue11;
            inv.dynTxtPropertyValue12 = inventory.dynTxtPropertyValue12;
            inv.dynTxtPropertyValue13 = inventory.dynTxtPropertyValue13;
            inv.dynTxtPropertyValue14 = inventory.dynTxtPropertyValue14;
            inv.dynTxtPropertyValue15 = inventory.dynTxtPropertyValue15;
            inv.dynTxtPropertyValue16 = inventory.dynTxtPropertyValue16;
            inv.dynTxtPropertyValue17 = inventory.dynTxtPropertyValue17;
            inv.dynTxtPropertyValue18 = inventory.dynTxtPropertyValue18;
            inv.dynTxtPropertyValue19 = inventory.dynTxtPropertyValue19;
            inv.dynTxtPropertyValue20 = inventory.dynTxtPropertyValue20;
            inv.dynDatePropertyValue01 = inventory.dynDatePropertyValue01;
            inv.dynDatePropertyValue02 = inventory.dynDatePropertyValue02;
            inv.dynDatePropertyValue03 = inventory.dynDatePropertyValue03;
            inv.dynDatePropertyValue04 = inventory.dynDatePropertyValue04;
            inv.dynDatePropertyValue05 = inventory.dynDatePropertyValue05;
            inv.dynDatePropertyValue06 = inventory.dynDatePropertyValue06;
            inv.dynDatePropertyValue07 = inventory.dynDatePropertyValue07;
            inv.dynDatePropertyValue08 = inventory.dynDatePropertyValue08;
            inv.dynDatePropertyValue09 = inventory.dynDatePropertyValue09;
            inv.dynDatePropertyValue10 = inventory.dynDatePropertyValue10;
            inv.profiles = inventory.profiles;
            inv.receivedTime = inventory.receivedTime;
            inv.receivedBy = inventory.receivedBy;
            inv.palletNo = inventory.palletNo;
            inv.adjustOutTime = inventory.adjustOutTime;
            inv.adjustOutBy = inventory.adjustOutBy;
            inv.shippedTime = inventory.shippedTime;
            inv.shippedBy = inventory.shippedBy;
            return inv;
        }
    }

    public static Builder<?> builder() {
        return new Builder<>();
    }
}
