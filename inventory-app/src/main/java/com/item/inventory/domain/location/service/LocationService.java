package com.item.inventory.domain.location.service;

import com.item.inventory.application.location.dto.LocationSearch;
import com.item.inventory.domain.location.model.entity.Location;
import com.item.inventory.domain.location.model.valueobject.DockStatus;
import com.item.inventory.domain.location.model.valueobject.LocationCategory;
import com.item.inventory.domain.location.model.valueobject.LocationSpaceStatus;
import com.item.inventory.domain.location.model.valueobject.LocationStatus;
import com.item.inventory.domain.location.model.valueobject.LocationType;
import com.item.inventory.domain.location.repository.LocationRepository;
import com.item.inventory.infrastructure.enums.ErrorCode;
import com.item.inventory.infrastructure.utils.Strings;
import com.item.xms.persistence.query.GenericRepository;
import com.item.xms.web.exception.BadRequestException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class LocationService {

    private final LocationRepository locationRepository;

    private final GenericRepository genericRepository;


    @Transactional
    public Location create(Location location) {
        if (Strings.isNotEmpty(location.getName())) {
            location.setName(location.getName().trim());
        }
        if (location.getCategory() == null) {
            if (location.getType().equals(LocationType.DOCK)) {
                location.setCategory(LocationCategory.DOCK);
            } else {
                location.setCategory(LocationCategory.WAREHOUSE);
            }
        }
        if (location.getDockStatus() == null && LocationType.DOCK.equals(location.getType())) {
            location.setDockStatus(DockStatus.AVAILABLE);
        }
        if (location.getStatus() == null) {
            location.setStatus(LocationStatus.USABLE);
        }
        Optional<Location> locationOptional = locationRepository.insert(location);
        return locationOptional.orElseThrow(()->new BadRequestException(ErrorCode.LOCATION_CREATE_FAILED));
    }

    @Transactional
    public Location update(Location location) {
        if (Strings.isNotEmpty(location.getName())) {
            location.setName(location.getName().trim());
        }

        clearDepletedFlagIfNeed(location);

        Optional<Location> locationOptional = locationRepository.update(location);
        return locationOptional.orElseThrow(()->new BadRequestException(ErrorCode.LOCATION_UPDATE_FAILED));
    }

    @Transactional
    public void batchUpdate(List<Location> locations) {
        locations.forEach(this::clearDepletedFlagIfNeed);
        locationRepository.batchUpdate(locations);
    }

    @Transactional
    public Location get(String id) {
        Optional<Location> locationOptional = locationRepository.get(id);
        return locationOptional.orElseThrow(()->new BadRequestException(ErrorCode.LOCATION_NOT_FOUND));
    }

    @Transactional
    public void delete(String id) {
        locationRepository.delete(id);
    }

    public void updateByEntryIdDockStatus(String id, String entryId, DockStatus dockStatus) {
        locationRepository.updateByEntryIdDockStatus(id, entryId, dockStatus);
    }

    public List<Location> search(LocationSearch search) {
        return genericRepository.list(Location.class, search).stream().toList();
    }

    public void clearDepletedFlagIfNeed(Location location) {
        if (Objects.equals(LocationSpaceStatus.EMPTY, location.getSpaceStatus())) {
            location.setDepletedFlag(false);
        }
    }
}
