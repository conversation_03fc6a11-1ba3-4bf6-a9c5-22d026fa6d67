package com.item.inventory.domain.location.repository;


import com.item.inventory.domain.location.model.entity.Location;
import com.item.inventory.domain.location.model.valueobject.DockStatus;

import java.util.List;
import java.util.Optional;

public interface LocationRepository {


    Optional<Location> insert(Location location);

    Optional<Location> update(Location location);

    boolean batchUpdate(List<Location> locations);

    Optional<Location> get(String id);

    boolean delete(String id);

    void updateByEntryIdDockStatus(String id, String entryId, DockStatus dockStatus);
}
