package com.item.inventory.domain.lp.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.item.inventory.domain.lp.model.valueobject.HlpCategory;
import com.item.inventory.domain.lp.model.valueobject.LinearUnit;
import com.item.inventory.domain.lp.model.valueobject.LpStatus;
import com.item.inventory.domain.lp.model.valueobject.LpType;
import com.item.inventory.domain.lp.model.valueobject.WeightUnit;
import lombok.Builder;
import lombok.Setter;

import java.time.LocalDateTime;

@Builder
public class LpBuilder {

    @Setter
    @TableId
    private String id;
    private String code;
    private String locationId;
    private LpType type;
    private HlpCategory hlpCategory;
    private String hlpReference;
    private String confId;
    private String orderId;
    private String taskId;
    private String trackingNo;
    private String cartonNo;
    private String palletNo;
    private String equipmentId;
    private Integer seq;
    private LpStatus status;
    private String parentId;
    private Double weight;
    private WeightUnit weightUnit;
    private Double length;
    private Double width;
    private Double height;
    private LinearUnit linearUnit;
    private String packagingTypeSpecId;
    private String createdBy;
    private LocalDateTime createdTime;
    private String updatedBy;
    private LocalDateTime updatedTime;
    private Integer stack;

    @SuppressWarnings("unused")
    public Lp toLp() {
        Lp lp = new Lp();
        lp.setId(id);
        lp.setCode(code);
        lp.setLocationId(locationId);
        lp.setType(type);
        lp.setHlpCategory(hlpCategory);
        lp.setHlpReference(hlpReference);
        lp.setConfId(confId);
        lp.setOrderId(orderId);
        lp.setTaskId(taskId);
        lp.setTrackingNo(trackingNo);
        lp.setEquipmentId(equipmentId);
        lp.setCartonNo(cartonNo);
        lp.setPalletNo(palletNo);
        lp.setSeq(seq);
        lp.setStatus(status);
        lp.setParentId(parentId);
        lp.setWeight(weight);
        lp.setWeightUnit(weightUnit);
        lp.setLength(length);
        lp.setWidth(width);
        lp.setHeight(height);
        lp.setLinearUnit(linearUnit);
        lp.setCreatedBy(createdBy);
        lp.setUpdatedBy(updatedBy);
        lp.setPackagingTypeSpecId(packagingTypeSpecId);
        lp.setStack(stack);
        return lp;
    }

}
