package com.item.inventory.domain.lp.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.item.inventory.domain.common.BaseEntity;
import com.item.inventory.domain.lp.model.valueobject.HlpCategory;
import com.item.inventory.domain.lp.model.valueobject.LinearUnit;
import com.item.inventory.domain.lp.model.valueobject.LpStatus;
import com.item.inventory.domain.lp.model.valueobject.LpType;
import com.item.inventory.domain.lp.model.valueobject.WeightUnit;
import com.item.inventory.infrastructure.annotation.InventoryIsolation;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@InventoryIsolation
@TableName("`inventory_service`.doc_lp")
public class Lp extends BaseEntity {
    @TableId
    private String id;

    private String code;
    private String locationId;
    private LpType type;
    private HlpCategory hlpCategory;
    private String hlpReference;
    private String confId;
    private String orderId;
    private String taskId;
    private String trackingNo;
    private String cartonNo;
    private String palletNo;
    private String equipmentId;
    private Integer seq;
    private LpStatus status;
    private String parentId;
    private String consolidateLp;
    private Double weight;
    private WeightUnit weightUnit;
    private Double length;
    private Double width;
    private Double height;
    private LinearUnit linearUnit;
    private String packagingTypeSpecId;
    private Boolean isPartialPallet;
    private Integer stack;

    public static Lp create(LpBuilder builder) {
        return builder.toLp();
    }


}
