package com.item.inventory.application.location.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.item.inventory.domain.location.repository.LocationRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.item.inventory.application.location.assembler.LocationAssembler;
import com.item.inventory.application.location.dto.CreateLocationCmd;
import com.item.inventory.application.location.dto.DockOperator;
import com.item.inventory.application.location.dto.LocationDto;
import com.item.inventory.application.location.dto.LocationSearch;
import com.item.inventory.application.location.dto.UpdateLocationCmd;
import com.item.inventory.application.lp.dto.CreateLpCmd;
import com.item.inventory.application.lp.dto.LpDto;
import com.item.inventory.application.lp.service.LpApplicationService;
import com.item.inventory.domain.common.Constants;
import com.item.inventory.domain.location.model.entity.Location;
import com.item.inventory.domain.location.model.valueobject.DockStatus;
import com.item.inventory.domain.location.model.valueobject.LocationStatus;
import com.item.inventory.domain.location.model.valueobject.LocationType;
import com.item.inventory.domain.location.service.LocationService;
import com.item.inventory.domain.lp.model.valueobject.HlpCategory;
import com.item.inventory.domain.lp.model.valueobject.LpType;
import com.item.inventory.domain.sequences.service.SequencesService;
import com.item.inventory.infrastructure.enums.ErrorCode;
import com.item.inventory.infrastructure.utils.Lists;
import com.item.inventory.infrastructure.utils.Strings;
import com.item.xms.persistence.query.GenericRepository;
import com.item.xms.persistence.query.PageResult;
import com.item.xms.web.exception.BadRequestException;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LocationApplicationService {

    private final LocationService locationService;
    private final LpApplicationService lpApplicationService;

    private final LocationAssembler locationAssembler;

    private final SequencesService sequencesService;

    private final GenericRepository genericRepository;

    private final LocationRepository locationRepository;

    @Transactional
    public LocationDto createLocation(CreateLocationCmd cmd) {
        validateCreteLocationCmd(cmd);
        Location location = locationAssembler.toLocation(cmd);
        location.setName(location.getName().trim());
        location.generateId(sequencesService.getNextSequence(Constants.LOCATION_COUNTER_ID));

        if (LocationType.PICK.equals(location.getType()) && Strings.isEmpty(location.getHlpId())) {
            CreateLpCmd lp = CreateLpCmd.builder()
                    .type(LpType.HLP)
                    .hlpCategory(HlpCategory.LOCATION_ALIAS)
                    .hlpReference(location.getId())
                    .locationId(location.getId())
                    .build();
            LpDto hlp = lpApplicationService.createLp(lp);
            location.setHlpId(hlp.getId());
        }

        LocationDto locRec = locationAssembler.toDto(locationService.create(location));
        return locRec;
    }

    @Transactional
    public void updateLocation(UpdateLocationCmd cmd) {
        Location location = locationAssembler.toLocation(cmd);
        if (LocationType.PICK.equals(cmd.getType()) && Strings.isEmpty(cmd.getHlpId())) {
            LocationDto locDto = getLocation(cmd.getId());
            if (Strings.isEmpty(locDto.hlpId())) {
                CreateLpCmd lp = CreateLpCmd.builder()
                        .type(LpType.HLP)
                        .hlpCategory(HlpCategory.LOCATION_ALIAS)
                        .hlpReference(locDto.id())
                        .locationId(locDto.id())
                        .build();
                LpDto hlp = lpApplicationService.createLp(lp);
                location.setHlpId(hlp.getId());
            }
        }
        locationService.update(location);
    }

    @Transactional
    public void batchUpdateLocation(List<UpdateLocationCmd> cmds) {
        List<Location> locations = locationAssembler.toLocations(cmds);
        locationService.batchUpdate(locations);
    }

    public List<LocationDto> importLocation(List<CreateLocationCmd> batch) {
        if (CollectionUtils.isEmpty(batch)) return Lists.newArrayList();

        LocationSearch search = new LocationSearch();
        search.names = batch.stream().map(o -> o.name().trim()).toList();
        List<LocationDto> locations = search(search);
        Map<String, LocationDto> locationMap = locations.stream().collect(Collectors.toMap(o -> o.name(), o -> o));

        List<LocationDto> res = Lists.newArrayList();
        batch.forEach(o -> {
            String name = o.name().trim();
            if (locationMap.containsKey(name)) {
                LocationDto location = locationMap.get(name);
                Location update = locationAssembler.toLocation(o);
                update.setId(location.id());

                if (update.getStatus() == null) {
                    update.setStatus(LocationStatus.USABLE);
                }
                locationService.update(update);
                res.add(getLocation(location.id()));

            } else {
                LocationDto loc = createLocation(o);
                res.add(loc);
            }
        });

        return res;
    }

    private void validateCreteLocationCmd(CreateLocationCmd cmd) {
        if ((cmd.type() != LocationType.DOCK ) && cmd.linearUnit() == null) {
            throw new BadRequestException(ErrorCode.INVALID_PARAMETER, "linearUnit is required for non-DOCK locations name:"+ cmd.name());
        }
    }

    @Transactional
    public LocationDto getLocation(String id) {
        return locationAssembler.toDto(locationService.get(id));
    }

    @Transactional
    public List<LocationDto> search(LocationSearch search) {
        // 处理search为null的情况
        if (search == null) {
            search = new LocationSearch();
        }
        
        List<Location> locationList = locationService.search(search);
        
        // 如果search中包含regexName字段，则按照全名匹配排在最前，然后是name升序
        if (search.regexName != null && !search.regexName.isEmpty()) {
            String searchName = search.regexName;
            List<LocationDto> dtoList = locationAssembler.toDto(locationList);
            return dtoList.stream()
                .sorted((loc1, loc2) -> {
                    // 全名匹配的排在最前面
                    boolean exactMatch1 = loc1.name() != null && loc1.name().equals(searchName);
                    boolean exactMatch2 = loc2.name() != null && loc2.name().equals(searchName);
                    
                    if (exactMatch1 && !exactMatch2) {
                        return -1;
                    } else if (!exactMatch1 && exactMatch2) {
                        return 1;
                    } else {
                        // 否则按name升序排序，处理null值情况
                        String name1 = loc1.name() != null ? loc1.name() : "";
                        String name2 = loc2.name() != null ? loc2.name() : "";
                        return name1.compareTo(name2);
                    }
                })
                .collect(java.util.stream.Collectors.toList());
        } else {
            // 如果没有regexName搜索条件，只按name升序排序
            List<LocationDto> dtoList = locationAssembler.toDto(locationList);
            return dtoList.stream()
                .sorted((loc1, loc2) -> {
                    // 处理null值情况
                    String name1 = loc1.name() != null ? loc1.name() : "";
                    String name2 = loc2.name() != null ? loc2.name() : "";
                    return name1.compareTo(name2);
                })
                .collect(java.util.stream.Collectors.toList());
        }
    }

    @Transactional
    public void deleteLocation(String id) {
        locationService.delete(id);
    }

    public PageResult<LocationDto> searchByPaging(LocationSearch search) {
        // 处理search为null的情况
        if (search == null) {
            search = new LocationSearch();
        }
        
        try {
            PageResult<Location> result = genericRepository.page(Location.class, search);
            PageResult<LocationDto> dtoResult = locationAssembler.toDto(result);
            
            // 防止getList()返回null
            if (dtoResult.getList() == null) {
                dtoResult.setList(java.util.Collections.emptyList());
                return dtoResult;
            }
            
            // 如果search中包含regexName字段，则按照全名匹配排在最前，然后是name升序
            if (search.regexName != null && !search.regexName.isEmpty()) {
                String searchName = search.regexName;
                
                try {
                    // 获取列表内容并重新排序
                    List<LocationDto> sortedList = dtoResult.getList().stream()
                        .sorted((loc1, loc2) -> {
                            try {
                                // 全名匹配的排在最前面
                                boolean exactMatch1 = loc1 != null && loc1.name() != null && loc1.name().equals(searchName);
                                boolean exactMatch2 = loc2 != null && loc2.name() != null && loc2.name().equals(searchName);
                                
                                if (exactMatch1 && !exactMatch2) {
                                    return -1;
                                } else if (!exactMatch1 && exactMatch2) {
                                    return 1;
                                } else {
                                    // 否则按name升序排序，处理null值情况
                                    String name1 = (loc1 != null && loc1.name() != null) ? loc1.name() : "";
                                    String name2 = (loc2 != null && loc2.name() != null) ? loc2.name() : "";
                                    return name1.compareTo(name2);
                                }
                            } catch (Exception e) {
                                // 任何异常发生时，默认规则
                                return 0;
                            }
                        })
                        .collect(java.util.stream.Collectors.toList());
                    
                    // 将排序后的内容设置回pageResult
                    dtoResult.setList(sortedList);
                } catch (Exception e) {
                    // 排序过程中发生异常，保留原有顺序
                    System.err.println("Sorting failed in regexName condition: " + e.getMessage());
                }
            } else {
                try {
                    // 如果没有regexName搜索条件，只按name升序排序
                    List<LocationDto> sortedList = dtoResult.getList().stream()
                        .sorted((loc1, loc2) -> {
                            try {
                                // 处理null值情况
                                String name1 = (loc1 != null && loc1.name() != null) ? loc1.name() : "";
                                String name2 = (loc2 != null && loc2.name() != null) ? loc2.name() : "";
                                return name1.compareTo(name2);
                            } catch (Exception e) {
                                // 任何异常发生时，默认规则
                                return 0;
                            }
                        })
                        .collect(java.util.stream.Collectors.toList());
                    
                    // 将排序后的内容设置回pageResult
                    dtoResult.setList(sortedList);
                } catch (Exception e) {
                    // 排序过程中发生异常，保留原有顺序
                    System.err.println("Sorting by name in ascending order failed: " + e.getMessage());
                }
            }
            
            return dtoResult;
        } catch (Exception e) {
            // 记录错误并返回空结果
            System.err.println("Exception in LocationApplicationService.searchByPaging: " + e.getMessage());
            
            // 使用locationAssembler创建空PageResult
            PageResult<Location> emptyResult = genericRepository.page(Location.class, search);
            if (emptyResult.getList() != null) {
                emptyResult.getList().clear();
            } else {
                emptyResult.setList(java.util.Collections.emptyList());
            }
            
            // 使用Assembler转换为DTO结果
            return locationAssembler.toDto(emptyResult);
        }
    }

    @Transactional
    public void reserveDock(String id, DockOperator.ReserveDock operator) {
        LocationDto locationDto = getLocation(id);
        if (!LocationType.DOCK.equals(locationDto.type())) {
            throw new BadRequestException(ErrorCode.LOCATION_NOT_DOCK);
        }
        
        Location location = locationService.get(id);
        location.setDockStatus(DockStatus.RESERVED);
        location.setEntryId(operator.entryId());
        
        locationService.update(location);
    }

    @Transactional
    public void releaseDock(String id) {
        LocationDto locationDto = getLocation(id);
        if (!LocationType.DOCK.equals(locationDto.type())) {
            throw new BadRequestException(ErrorCode.LOCATION_NOT_DOCK);
        }
        
        Location location = locationService.get(id);
        location.setDockStatus(DockStatus.AVAILABLE);

        locationService.update(location);
    }

    @Transactional
    public void occupyDock(String id, DockOperator.OccupyDock operator) {
        LocationDto locationDto = getLocation(id);
        if (!LocationType.DOCK.equals(locationDto.type())) {
            throw new BadRequestException(ErrorCode.LOCATION_NOT_DOCK);
        }

        Location location = locationService.get(id);
        location.setDockStatus(DockStatus.OCCUPIED);
        location.setEntryId(operator.entryId());

        locationService.update(location);
    }

    @Transactional
    public void dockSyncFromWise(String dockId, DockOperator.SyncDock operator) {
        Optional<Location> locationOptional = locationRepository.get(dockId);
        if (locationOptional.isEmpty()) return;
        locationService.updateByEntryIdDockStatus(dockId, operator.entryId(), operator.dockStatus());
    }

}
