package com.item.inventory.application.inventory.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.item.inventory.domain.inventory.model.entity.InventoryProfile;
import com.item.inventory.domain.inventory.model.valueobject.DiscrepancyFlag;
import com.item.inventory.domain.inventory.model.valueobject.InventoryChannel;
import com.item.inventory.domain.inventory.model.valueobject.InventoryMode;
import com.item.inventory.domain.inventory.model.valueobject.InventoryStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class InventoryDto implements Serializable  {
    private Long id;
    private String customerId;
    private  String titleId;
    private String itemId;
    private Double qty;
    private String uomId;
    private String uom;
    private String sn;
    private Double baseQty;
    public Double secondaryQty;
    public String secondaryUomId;
    private InventoryStatus status;
    private InventoryStatus preStatus;
    private String type;
    private InventoryMode mode;
    private InventoryChannel channel;
    private String lotNo;
    private LocalDateTime expirationDate;
    private LocalDateTime mfgDate;
    private Integer shelfLifeDays;
    private String lpId;
    private String locationId;
    private String receiptId;
    private String orderId;
    private String adjustmentId;
    private String originalLPId;
    private Double originalBaseQty;
    private String supplierId;
    private String receiveTaskId;
    private String putAwayTaskId;
    private String pickTaskId;
    private String packTaskId;
    private String loadTaskId;
    private String workflowTaskId;
    private String replenishTaskId;
    private String movementTaskId;

    private String dynTxtPropertyValue01;
    private String dynTxtPropertyValue02;
    private String dynTxtPropertyValue03;
    private String dynTxtPropertyValue04;
    private String dynTxtPropertyValue05;
    private String dynTxtPropertyValue06;
    private String dynTxtPropertyValue07;
    private String dynTxtPropertyValue08;
    private String dynTxtPropertyValue09;
    private String dynTxtPropertyValue10;
    private String dynTxtPropertyValue11;
    private String dynTxtPropertyValue12;
    private String dynTxtPropertyValue13;
    private String dynTxtPropertyValue14;
    private String dynTxtPropertyValue15;
    private String dynTxtPropertyValue16;
    private String dynTxtPropertyValue17;
    private String dynTxtPropertyValue18;
    private String dynTxtPropertyValue19;
    private String dynTxtPropertyValue20;

    private LocalDateTime dynDatePropertyValue01;
    private LocalDateTime dynDatePropertyValue02;
    private LocalDateTime dynDatePropertyValue03;
    private LocalDateTime dynDatePropertyValue04;
    private LocalDateTime dynDatePropertyValue05;
    private LocalDateTime dynDatePropertyValue06;
    private LocalDateTime dynDatePropertyValue07;
    private LocalDateTime dynDatePropertyValue08;
    private LocalDateTime dynDatePropertyValue09;
    private LocalDateTime dynDatePropertyValue10;

    private List<InventoryProfile> profiles;

    private LocalDateTime receivedTime;
    private String receivedBy;
    
    private String palletNo;

    private LocalDateTime adjustOutTime;
    private String adjustOutBy;

    private LocalDateTime shippedTime;
    private String shippedBy;
    private DiscrepancyFlag discrepancyFlag;
    private LocalDateTime discrepancyReportTime;

    private String loadId;
    private String trackingNo;

    public String createdBy;
    public LocalDateTime createdTime;
    public String updatedBy;
    public LocalDateTime updatedTime;

}
