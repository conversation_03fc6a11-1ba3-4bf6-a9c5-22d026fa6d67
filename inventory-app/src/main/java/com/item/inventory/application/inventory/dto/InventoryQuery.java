package com.item.inventory.application.inventory.dto;

import com.item.inventory.domain.inventory.model.valueobject.InventoryChannel;
import com.item.inventory.domain.inventory.model.valueobject.InventoryMode;
import com.item.inventory.domain.inventory.model.valueobject.InventoryStatus;
import com.item.xms.persistence.query.PageQuery;
import com.item.xms.persistence.query.QueryCondition;
import com.item.xms.persistence.query.QueryOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InventoryQuery extends PageQuery {
    @QueryCondition(field = "id", operator = QueryOperator.EQ)
    public String id;

    @QueryCondition(field = "customerId", operator = QueryOperator.EQ)
    public String customerId;
    @QueryCondition(field = "customerId", operator = QueryOperator.IN)
    public List<String> customerIds;

    @QueryCondition(field = "titleId", operator = QueryOperator.EQ)
    public String titleId;
    @QueryCondition(field = "titleId", operator = QueryOperator.IN)
    public List<String> titleIds;

    @QueryCondition(field = "itemId", operator = QueryOperator.EQ)
    public String itemId;
    @QueryCondition(field = "itemId", operator = QueryOperator.IN)
    public List<String> itemIds;

    @QueryCondition(field = "qty", operator = QueryOperator.GT)
    public Double qtyGt;

    @QueryCondition(field = "sn", operator = QueryOperator.EQ)
    public String sn;

    @QueryCondition(field = "sn", operator = QueryOperator.IN)
    public List<String> sns;

    @QueryCondition(field = "status", operator = QueryOperator.EQ)
    public InventoryStatus status;
    @QueryCondition(field = "status", operator = QueryOperator.IN)
    public List<InventoryStatus> statuses;
    @QueryCondition(field = "status", operator = QueryOperator.NOT_IN)
    public List<InventoryStatus> excludeStatuses;

    @QueryCondition(field = "type", operator = QueryOperator.EQ)
    public String type;
    @QueryCondition(field = "type", operator = QueryOperator.IN)
    public List<String> types;

    @QueryCondition(field = "mode", operator = QueryOperator.EQ)
    public InventoryMode mode;

    @QueryCondition(field = "channel", operator = QueryOperator.EQ)
    public InventoryChannel channel;

    @QueryCondition(field = "lotNo", operator = QueryOperator.EQ)
    public String lotNo;

    @QueryCondition(field = "lotNo", operator = QueryOperator.IN)
    public List<String> lotNos;

    @QueryCondition(field = "lpId", operator = QueryOperator.EQ)
    public String lpId;
    @QueryCondition(field = "lpId", operator = QueryOperator.IN)
    public List<String> lpIds;
    @QueryCondition(field = "lpId", operator = QueryOperator.NOT_IN)
    public List<String> excludeLpIds;

    @QueryCondition(field = "locationId", operator = QueryOperator.EQ)
    public String locationId;
    @QueryCondition(field = "locationId", operator = QueryOperator.IN)
    public List<String> locationIds;
    @QueryCondition(field = "locationId", operator = QueryOperator.NOT_IN)
    public List<String> excludeLocationIds;

    @QueryCondition(field = "receiptId", operator = QueryOperator.EQ)
    public String receiptId;
    @QueryCondition(field = "receiptId", operator = QueryOperator.IN)
    public List<String> receiptIds;

    @QueryCondition(field = "orderId", operator = QueryOperator.EQ)
    public String orderId;
    @QueryCondition(field = "orderId", operator = QueryOperator.IN)
    public List<String> orderIds;

    @QueryCondition(field = "adjustmentId", operator = QueryOperator.EQ)
    public String adjustmentId;
    @QueryCondition(field = "adjustmentId", operator = QueryOperator.IN)
    public List<String> adjustmentIds;

    @QueryCondition(field = "receivedTime", operator = QueryOperator.GTE)
    public LocalDateTime receivedTimeFrom;
    @QueryCondition(field = "receivedTime", operator = QueryOperator.LTE)
    public LocalDateTime receivedTimeTo;

    @QueryCondition(field = "shippedTime", operator = QueryOperator.GTE)
    public LocalDateTime shippedTimeFrom;
    @QueryCondition(field = "shippedTime", operator = QueryOperator.LTE)
    public LocalDateTime shippedTimeTo;

    @QueryCondition(field = "createdTime", operator = QueryOperator.GTE)
    public LocalDateTime createdTimeFrom;
    @QueryCondition(field = "createdTime", operator = QueryOperator.LTE)
    public LocalDateTime createdTimeTo;

    @QueryCondition(field = "supplierId", operator = QueryOperator.EQ)
    public String supplierId;
    @QueryCondition(field = "supplierId", operator = QueryOperator.IN)
    public List<String> supplierIds;

    @QueryCondition(field = "receiveTaskId", operator = QueryOperator.EQ)
    public String receiveTaskId;
    @QueryCondition(field = "receiveTaskId", operator = QueryOperator.IN)
    public List<String> receiveTaskIds;

    @QueryCondition(field = "putAwayTaskId", operator = QueryOperator.EQ)
    public String putAwayTaskId;
    @QueryCondition(field = "putAwayTaskId", operator = QueryOperator.IN)
    public List<String> putAwayTaskIds;

    @QueryCondition(field = "pickTaskId", operator = QueryOperator.EQ)
    public String pickTaskId;
    @QueryCondition(field = "pickTaskId", operator = QueryOperator.IN)
    public List<String> pickTaskIds;

    @QueryCondition(field = "packTaskId", operator = QueryOperator.EQ)
    public String packTaskId;
    @QueryCondition(field = "packTaskId", operator = QueryOperator.IN)
    public List<String> packTaskIds;

    @QueryCondition(field = "loadTaskId", operator = QueryOperator.EQ)
    public String loadTaskId;
    @QueryCondition(field = "loadTaskId", operator = QueryOperator.IN)
    public List<String> loadTaskIds;

    @QueryCondition(field = "workflowTaskId", operator = QueryOperator.EQ)
    public String workflowTaskId;
    @QueryCondition(field = "workflowTaskId", operator = QueryOperator.IN)
    public List<String> workflowTaskIds;

    @QueryCondition(field = "replenishTaskId", operator = QueryOperator.EQ)
    public String replenishTaskId;
    @QueryCondition(field = "replenishTaskId", operator = QueryOperator.IN)
    public List<String> replenishTaskIds;

    @QueryCondition(field = "movementTaskId", operator = QueryOperator.EQ)
    public String movementTaskId;
    @QueryCondition(field = "movementTaskId", operator = QueryOperator.IN)
    public List<String> movementTaskIds;

    @QueryCondition(field = "dynTxtPropertyValue01", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue01;
    @QueryCondition(field = "dynTxtPropertyValue01", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue01s;

    @QueryCondition(field = "dynTxtPropertyValue02", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue02;
    @QueryCondition(field = "dynTxtPropertyValue02", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue02s;

    @QueryCondition(field = "dynTxtPropertyValue03", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue03;
    @QueryCondition(field = "dynTxtPropertyValue03", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue03s;

    @QueryCondition(field = "dynTxtPropertyValue04", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue04;
    @QueryCondition(field = "dynTxtPropertyValue04", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue04s;

    @QueryCondition(field = "dynTxtPropertyValue05", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue05;
    @QueryCondition(field = "dynTxtPropertyValue05", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue05s;

    @QueryCondition(field = "dynTxtPropertyValue06", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue06;
    @QueryCondition(field = "dynTxtPropertyValue06", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue06s;

    @QueryCondition(field = "dynTxtPropertyValue07", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue07;
    @QueryCondition(field = "dynTxtPropertyValue07", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue07s;

    @QueryCondition(field = "dynTxtPropertyValue08", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue08;
    @QueryCondition(field = "dynTxtPropertyValue08", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue08s;

    @QueryCondition(field = "dynTxtPropertyValue09", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue09;
    @QueryCondition(field = "dynTxtPropertyValue09", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue09s;

    @QueryCondition(field = "dynTxtPropertyValue10", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue10;
    @QueryCondition(field = "dynTxtPropertyValue10", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue10s;

    @QueryCondition(field = "dynTxtPropertyValue11", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue11;
    @QueryCondition(field = "dynTxtPropertyValue11", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue11s;

    @QueryCondition(field = "dynTxtPropertyValue12", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue12;
    @QueryCondition(field = "dynTxtPropertyValue12", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue12s;

    @QueryCondition(field = "dynTxtPropertyValue13", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue13;
    @QueryCondition(field = "dynTxtPropertyValue13", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue13s;

    @QueryCondition(field = "dynTxtPropertyValue14", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue14;
    @QueryCondition(field = "dynTxtPropertyValue14", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue14s;

    @QueryCondition(field = "dynTxtPropertyValue15", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue15;
    @QueryCondition(field = "dynTxtPropertyValue15", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue15s;

    @QueryCondition(field = "dynTxtPropertyValue16", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue16;
    @QueryCondition(field = "dynTxtPropertyValue16", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue16s;

    @QueryCondition(field = "dynTxtPropertyValue17", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue17;
    @QueryCondition(field = "dynTxtPropertyValue17", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue17s;

    @QueryCondition(field = "dynTxtPropertyValue18", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue18;
    @QueryCondition(field = "dynTxtPropertyValue18", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue18s;

    @QueryCondition(field = "dynTxtPropertyValue19", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue19;
    @QueryCondition(field = "dynTxtPropertyValue19", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue19s;

    @QueryCondition(field = "dynTxtPropertyValue20", operator = QueryOperator.EQ)
    public String dynTxtPropertyValue20;
    @QueryCondition(field = "dynTxtPropertyValue20", operator = QueryOperator.IN)
    public List<String> dynTxtPropertyValue20s;

    public Boolean includeInnerLp;
    public Boolean includeAdjustOut;

    @QueryCondition(field = "expirationDate", operator = QueryOperator.IS_NOT_NULL)
    public Boolean expirationDateNotNull;

    @QueryCondition(field = "mfgDate", operator = QueryOperator.IS_NOT_NULL)
    public Boolean mfgDateNotNull;

    @QueryCondition(field = "uomId", operator = QueryOperator.EQ)
    public String uomId;

    @QueryCondition(field = "expirationDate", operator = QueryOperator.EQ)
    public LocalDateTime expirationDate;

    @QueryCondition(field = "mfgDate", operator = QueryOperator.EQ)
    public LocalDateTime mfgDate;

    @QueryCondition(field = "shelfLifeDays", operator = QueryOperator.EQ)
    public Integer shelfLifeDays;

    @QueryCondition(field = "loadId", operator = QueryOperator.EQ)
    public String loadId;

    @QueryCondition(field = "loadId", operator = QueryOperator.IN)
    public List<String> loadIds;

    @QueryCondition(field = "trackingNo", operator = QueryOperator.EQ)
    public String trackingNo;

    @QueryCondition(field = "trackingNo", operator = QueryOperator.IN)
    public List<String> trackingNos;
}
