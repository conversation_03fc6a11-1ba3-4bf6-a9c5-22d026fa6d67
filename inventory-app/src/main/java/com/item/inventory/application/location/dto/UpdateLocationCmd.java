package com.item.inventory.application.location.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.item.inventory.domain.inventory.model.valueobject.InventoryStatus;
import com.item.inventory.domain.location.model.valueobject.*;
import com.item.xms.persistence.cmd.NullifyFieldCmd;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateLocationCmd {
    String id;
    @NotBlank String name;
    String akaName;
    Integer floor;
    String parentId;
    LocationType type;
    Double maxSize;
    Double length;
    Double width;
    Double height;
    LinearUnit linearUnit;
    LocationStatus status;
    LocationSpaceStatus spaceStatus;
    DockStatus dockStatus;
    String hlpId;
    PickType supportPickType;
    LocationCategory category;
    Integer sequence;
    LocationCapacityType capacityType;
    Double capacity;
    DataChannel channel;
    PackageStack stack;
    List<String> customerIds;
    List<String> occupiedCustomerIds;
    List<String> rfids;
    String tag;
    String zone;
    String aisle;
    String bay;
    String section;
    Integer level;
    String slot;
    Boolean twoSigns;
    Boolean enableLocationHLP;
    Boolean disallowToMixItemOnSameLocation;
    Double putAwaySuggestionWeight;
    Double pickStrategyWeight;
    LocalDateTime lastCountDate;
    List<String> features;
    String entryId;
    TemperatureControl temperatureControl;
    Boolean enableDepleted;
    Boolean depletedFlag;

     // 动态文本字段
     String dynTxtPropertyValue01;
     String dynTxtPropertyValue02;
     String dynTxtPropertyValue03;
     String dynTxtPropertyValue04;
     String dynTxtPropertyValue05;
     String dynTxtPropertyValue06;
     String dynTxtPropertyValue07;
     String dynTxtPropertyValue08;
     String dynTxtPropertyValue09;
     String dynTxtPropertyValue10;
 
     // 动态时间字段
     LocalDateTime dynDatePropertyValue01;
     LocalDateTime dynDatePropertyValue02;
     LocalDateTime dynDatePropertyValue03;
     LocalDateTime dynDatePropertyValue04;
     LocalDateTime dynDatePropertyValue05;
     InventoryStatus inventoryStatus;
}
