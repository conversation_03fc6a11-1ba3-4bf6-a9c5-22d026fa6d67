package com.item.inventory.application.lp.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.item.inventory.domain.lp.model.valueobject.HlpCategory;
import com.item.inventory.domain.lp.model.valueobject.LinearUnit;
import com.item.inventory.domain.lp.model.valueobject.LpStatus;
import com.item.inventory.domain.lp.model.valueobject.LpType;
import com.item.inventory.domain.lp.model.valueobject.WeightUnit;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class LpDto {
    private String id;
    private String code;
    private String locationId;
    private LpType type;
    private HlpCategory hlpCategory;
    private String hlpReference;
    private String confId;
    private String orderId;
    private String taskId;
    private String trackingNo;
    private String cartonNo;
    private String palletNo;
    private String equipmentId;
    private Integer seq;
    private LpStatus status;
    private String parentId;
    private String consolidateLp;
    private Double weight;
    private WeightUnit weightUnit;
    private Double length;
    private Double width;
    private Double height;
    private LinearUnit linearUnit;
    private String packagingTypeSpecId;
    private String createdBy;
    private LocalDateTime createdTime;
    private String updatedBy;
    private LocalDateTime updatedTime;
    private String tenantId;
    public String isolationId;
    private Boolean isPartialPallet;
    private Integer stack;
}
