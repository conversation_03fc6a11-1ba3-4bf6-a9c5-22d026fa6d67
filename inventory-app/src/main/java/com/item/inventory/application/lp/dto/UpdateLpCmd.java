package com.item.inventory.application.lp.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.item.inventory.domain.lp.model.valueobject.HlpCategory;
import com.item.inventory.domain.lp.model.valueobject.LinearUnit;
import com.item.inventory.domain.lp.model.valueobject.LpStatus;
import com.item.inventory.domain.lp.model.valueobject.LpType;
import com.item.inventory.domain.lp.model.valueobject.WeightUnit;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateLpCmd {
    String id;
    String code;
    String locationId;
    LpType type;
    HlpCategory hlpCategory;
    String hlpReference;
    String confId;
    String orderId;
    String taskId;
    String trackingNo;
    String cartonNo;
    String palletNo;
    String equipmentId;
    Integer seq;
    LpStatus status;
    String parentId;
    String consolidateLp;
    Double weight;
    WeightUnit weightUnit;
    Double length;
    Double width;
    Double height;
    LinearUnit linearUnit;
    private String packagingTypeSpecId;
    String createdBy;
    LocalDateTime createdTime;
    String updatedBy;
    LocalDateTime updatedTime;
    private String tenantId;
    public String isolationId;
    private Boolean isPartialPallet;
    private List<String> nullifyFields;
    private Integer stack;
}
