FROM stage.logisticsteam.com:5000/app-base:aplpha2.0
RUN mkdir -p /sync/log && mkdir -p /data/inventory-service-backend/ && mkdir -p /archive/logs/inventory
COPY inventory-app/build/libs/inventory-app-0.0.1-PREVIEW-SNAPSHOT.jar /data/inventory-service-backend/inventory-app-0.0.1-SNAPSHOT.jar
COPY deploy/preview/aws/supervisord.conf /etc/supervisor/supervisord.conf
CMD ["/usr/bin/supervisord","-c","/etc/supervisor/supervisord.conf"]